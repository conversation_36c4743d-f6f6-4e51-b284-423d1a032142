<?php
/**
 * Xun Framework 测试主题函数文件
 * 
 * 这个文件包含了主题的基本功能设置和Xun Framework的使用示例。
 * 
 * @package Xun Test Theme
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 引入Xun Framework
 * 
 * 加载框架的主文件，启用框架功能。
 * 
 * @since 1.0
 */
require_once get_template_directory() . '/framework/xun-framework.php';

/**
 * 主题设置
 * 
 * 设置主题的基本功能和支持特性。
 * 
 * @since 1.0
 */
function xun_test_setup() {
    
    // 添加主题支持
    add_theme_support( 'title-tag' );
    add_theme_support( 'post-thumbnails' );
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ) );
    
    // 注册导航菜单
    register_nav_menus( array(
        'menu-1' => esc_html__( '主菜单', 'xun-test' ),
    ) );
    
    // 设置内容宽度
    $GLOBALS['content_width'] = 1200;
}
add_action( 'after_setup_theme', 'xun_test_setup' );

/**
 * 加载样式和脚本
 * 
 * 在前端加载主题的CSS和JavaScript文件。
 * 
 * @since 1.0
 */
function xun_test_scripts() {
    
    // 主题样式
    wp_enqueue_style( 'xun-test-style', get_stylesheet_uri(), array(), '1.0' );
    
    // 主题脚本
    wp_enqueue_script( 'xun-test-script', get_template_directory_uri() . '/js/theme.js', array( 'jquery' ), '1.0', true );
    
    // 评论回复脚本
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }
}
add_action( 'wp_enqueue_scripts', 'xun_test_scripts' );

/**
 * 注册侧边栏
 * 
 * 注册主题的小工具区域。
 * 
 * @since 1.0
 */
function xun_test_widgets_init() {
    register_sidebar( array(
        'name'          => esc_html__( '侧边栏', 'xun-test' ),
        'id'            => 'sidebar-1',
        'description'   => esc_html__( '在侧边栏中添加小工具。', 'xun-test' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );
}
add_action( 'widgets_init', 'xun_test_widgets_init' );

/**
 * 引入主题选项配置
 *
 * 从单独的配置文件中加载主题选项设置。
 *
 * @since 1.0
 */
require_once get_template_directory() . '/theme-options.php';

/**
 * 获取主题选项值
 * 
 * 便捷函数用于获取主题选项的值。
 * 
 * @since 1.0
 * 
 * @param string $option_id 选项ID
 * @param mixed  $default   默认值
 * 
 * @return mixed 选项值
 */
function xun_test_get_option( $option_id, $default = '' ) {
    
    if ( function_exists( 'xun_get_option' ) ) {
        return xun_get_option( 'xun_test_options', $option_id, $default );
    }
    
    return $default;
}

/**
 * 输出自定义CSS
 * 
 * 在页面头部输出用户自定义的CSS代码。
 * 
 * @since 1.0
 */
function xun_custom_css() {

    $custom_css = xun_get_theme_option( 'custom_css' );

    if ( ! empty( $custom_css ) ) {
        echo '<style type="text/css">' . wp_strip_all_tags( $custom_css ) . '</style>';
    }
}
add_action( 'wp_head', 'xun_custom_css' );

/**
 * 自定义网站标题
 * 
 * 如果设置了自定义标题，则使用自定义标题。
 * 
 * @since 1.0
 * 
 * @param string $title 原始标题
 * 
 * @return string 修改后的标题
 */
function xun_custom_title( $title ) {

    $custom_title = xun_get_theme_option( 'site_title' );

    if ( ! empty( $custom_title ) && is_home() ) {
        return $custom_title;
    }

    return $title;
}
add_filter( 'wp_title', 'xun_custom_title' );

/**
 * 自定义网站描述
 * 
 * 如果设置了自定义描述，则使用自定义描述。
 * 
 * @since 1.0
 * 
 * @param string $description 原始描述
 * 
 * @return string 修改后的描述
 */
function xun_custom_description( $description ) {

    $custom_description = xun_get_theme_option( 'site_description' );

    if ( ! empty( $custom_description ) ) {
        return $custom_description;
    }

    return $description;
}
add_filter( 'bloginfo', 'xun_custom_description' );

/**
 * 图标功能演示函数
 *
 * 以下函数演示了如何在主题中使用Xun Framework的图标功能。
 *
 * @since 1.0
 */

/**
 * 获取导航菜单图标
 *
 * 根据菜单项的标题返回对应的图标。
 *
 * @since 1.0
 *
 * @param string $menu_title 菜单标题
 *
 * @return string 图标HTML
 */
function xun_get_nav_icon( $menu_title ) {

    $icon_map = array(
        '首页'   => 'home',
        '关于'   => 'information-circle',
        '服务'   => 'cog-6-tooth',
        '产品'   => 'cube',
        '博客'   => 'document-text',
        '新闻'   => 'newspaper',
        '联系'   => 'envelope',
        '登录'   => 'user',
        '注册'   => 'user-plus',
        '购物车' => 'shopping-cart',
        '搜索'   => 'magnifying-glass',
        '设置'   => 'cog-6-tooth',
    );

    $icon_name = isset( $icon_map[ $menu_title ] ) ? $icon_map[ $menu_title ] : 'link';

    return xun_icon( $icon_name, array( 'class' => 'w-5 h-5 mr-2' ) );
}

/**
 * 显示状态消息
 *
 * 根据消息类型显示带图标的状态消息。
 *
 * @since 1.0
 *
 * @param string $message 消息内容
 * @param string $type    消息类型：success、error、warning、info
 *
 * @return string 状态消息HTML
 */
function xun_status_message( $message, $type = 'info' ) {

    $icon_map = array(
        'success' => array( 'icon' => 'check-circle', 'color' => 'text-green-600', 'bg' => 'bg-green-50' ),
        'error'   => array( 'icon' => 'x-circle', 'color' => 'text-red-600', 'bg' => 'bg-red-50' ),
        'warning' => array( 'icon' => 'exclamation-triangle', 'color' => 'text-yellow-600', 'bg' => 'bg-yellow-50' ),
        'info'    => array( 'icon' => 'information-circle', 'color' => 'text-blue-600', 'bg' => 'bg-blue-50' ),
    );

    $config = isset( $icon_map[ $type ] ) ? $icon_map[ $type ] : $icon_map['info'];

    $icon_html = xun_icon( $config['icon'], array(
        'style' => 'solid',
        'class' => 'w-5 h-5 ' . $config['color']
    ) );

    return '<div class="flex items-center gap-3 p-4 rounded-lg ' . $config['bg'] . '">' .
           $icon_html .
           '<span class="' . $config['color'] . '">' . esc_html( $message ) . '</span>' .
           '</div>';
}

/**
 * 获取社交媒体图标链接
 *
 * 生成社交媒体图标链接HTML。
 *
 * @since 1.0
 *
 * @param array $social_links 社交媒体链接数组
 *
 * @return string 社交媒体链接HTML
 */
function xun_social_icons( $social_links = array() ) {

    if ( empty( $social_links ) ) {
        // 从主题选项获取社交媒体链接
        $social_links = array(
            'facebook'  => xun_get_theme_option( 'facebook_url' ),
            'twitter'   => xun_get_theme_option( 'twitter_url' ),
            'instagram' => xun_get_theme_option( 'instagram_url' ),
            'linkedin'  => xun_get_theme_option( 'linkedin_url' ),
            'youtube'   => xun_get_theme_option( 'youtube_url' ),
        );
    }

    $icon_map = array(
        'facebook'  => 'facebook',
        'twitter'   => 'twitter',
        'instagram' => 'instagram',
        'linkedin'  => 'linkedin',
        'youtube'   => 'youtube',
        'github'    => 'github',
        'email'     => 'envelope',
        'phone'     => 'phone',
        'wechat'    => 'chat-bubble-left-right',
        'qq'        => 'chat-bubble-oval-left',
    );

    $output = '<div class="flex gap-4">';

    foreach ( $social_links as $platform => $url ) {
        if ( ! empty( $url ) && isset( $icon_map[ $platform ] ) ) {
            $icon_name = $icon_map[ $platform ];

            // 检查图标是否存在，如果不存在则使用通用链接图标
            if ( ! xun_icon_exists( $icon_name ) ) {
                $icon_name = 'link';
            }

            $icon_html = xun_icon( $icon_name, array( 'class' => 'w-6 h-6' ) );

            $output .= '<a href="' . esc_url( $url ) . '" target="_blank" rel="noopener" class="text-gray-600 hover:text-blue-600 transition-colors" title="' . ucfirst( $platform ) . '">';
            $output .= $icon_html;
            $output .= '</a>';
        }
    }

    $output .= '</div>';

    return $output;
}

/**
 * 获取功能特性图标
 *
 * 为功能特性显示相应的图标。
 *
 * @since 1.0
 *
 * @param string $feature_type 功能类型
 *
 * @return string 图标HTML
 */
function xun_feature_icon( $feature_type ) {

    $feature_icons = array(
        'speed'      => 'bolt',
        'security'   => 'shield-check',
        'responsive' => 'device-phone-mobile',
        'seo'        => 'magnifying-glass',
        'support'    => 'chat-bubble-left-right',
        'updates'    => 'arrow-path',
        'backup'     => 'cloud-arrow-up',
        'analytics'  => 'chart-bar',
        'ecommerce'  => 'shopping-cart',
        'blog'       => 'document-text',
        'gallery'    => 'photo',
        'video'      => 'play-circle',
        'audio'      => 'musical-note',
        'download'   => 'arrow-down-tray',
        'upload'     => 'arrow-up-tray',
        'edit'       => 'pencil',
        'delete'     => 'trash',
        'save'       => 'bookmark',
        'share'      => 'share',
        'print'      => 'printer',
        'email'      => 'envelope',
        'calendar'   => 'calendar-days',
        'clock'      => 'clock',
        'location'   => 'map-pin',
        'star'       => 'star',
        'heart'      => 'heart',
        'like'       => 'hand-thumb-up',
        'dislike'    => 'hand-thumb-down',
    );

    $icon_name = isset( $feature_icons[ $feature_type ] ) ? $feature_icons[ $feature_type ] : 'star';

    return xun_icon( $icon_name, array(
        'class' => 'w-8 h-8',
        'style' => 'solid'
    ) );
}



