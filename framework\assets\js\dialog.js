/**
 * XUN Framework Dialog Component
 * 弹窗组件
 * 
 * @version 1.0
 * <AUTHOR> Framework
 */

class XunDialog {
    constructor() {
        this.currentDialog = null;
        this.init();
    }

    /**
     * 初始化弹窗组件
     */
    init() {
        // 创建弹窗容器
        this.createDialogContainer();
        
        // 绑定ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentDialog) {
                this.close();
            }
        });
    }

    /**
     * 创建弹窗HTML容器
     */
    createDialogContainer() {
        const dialogHTML = `
            <div id="xun-dialog" role="dialog" aria-modal="true" aria-labelledby="dialog-title" class="relative hidden" style="z-index: 999999;">
                <!-- 背景遮罩 -->
                <div aria-hidden="true" class="fixed inset-0 bg-white/20 backdrop-blur-sm transition-all duration-300 opacity-0" id="dialog-backdrop" style="z-index: 999998;"></div>

                <div class="fixed inset-0 w-screen overflow-y-auto" style="z-index: 999999;">
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <!-- 弹窗面板 -->
                        <div id="dialog-panel" class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all duration-300 opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95 sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            <div>
                                <!-- 图标容器 -->
                                <div id="dialog-icon" class="mx-auto flex size-12 items-center justify-center rounded-full">
                                    <!-- 图标将动态插入 -->
                                </div>
                                
                                <!-- 标题和内容 -->
                                <div class="mt-3 text-center sm:mt-5">
                                    <h3 id="dialog-title" class="text-base font-semibold text-gray-900"></h3>
                                    <div class="mt-2">
                                        <p id="dialog-message" class="text-sm text-gray-500"></p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 按钮区域 -->
                            <div id="dialog-buttons" class="mt-5 sm:mt-6">
                                <!-- 按钮将动态插入 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        this.dialogElement = document.getElementById('xun-dialog');
        this.backdropElement = document.getElementById('dialog-backdrop');
        this.panelElement = document.getElementById('dialog-panel');
    }

    /**
     * 显示确认弹窗
     * @param {Object} options 配置选项
     * @returns {Promise} 返回用户选择的Promise
     */
    confirm(options = {}) {
        const config = {
            title: '确认操作',
            message: '您确定要执行此操作吗？',
            confirmText: '确定',
            cancelText: '取消',
            type: 'warning', // success, error, warning, info
            ...options
        };

        return new Promise((resolve) => {
            this.show(config, resolve, true);
        });
    }

    /**
     * 显示提示弹窗
     * @param {Object} options 配置选项
     * @returns {Promise} 返回Promise
     */
    alert(options = {}) {
        const config = {
            title: '提示',
            message: '操作完成',
            confirmText: '确定',
            type: 'info',
            ...options
        };

        return new Promise((resolve) => {
            this.show(config, resolve, false);
        });
    }

    /**
     * 显示弹窗
     * @param {Object} config 配置
     * @param {Function} resolve Promise resolve函数
     * @param {Boolean} isConfirm 是否为确认弹窗
     */
    show(config, resolve, isConfirm = false) {
        // 设置内容
        this.setContent(config);
        
        // 设置按钮
        this.setButtons(config, resolve, isConfirm);
        
        // 显示弹窗
        this.open();
        
        // 存储当前弹窗信息
        this.currentDialog = { resolve, isConfirm };
    }

    /**
     * 设置弹窗内容
     * @param {Object} config 配置
     */
    setContent(config) {
        // 设置图标
        const iconContainer = document.getElementById('dialog-icon');
        const iconConfig = this.getIconConfig(config.type);
        iconContainer.className = `mx-auto flex size-12 items-center justify-center rounded-full ${iconConfig.bgClass}`;
        iconContainer.innerHTML = iconConfig.icon;

        // 设置标题和消息
        document.getElementById('dialog-title').textContent = config.title;
        document.getElementById('dialog-message').textContent = config.message;
    }

    /**
     * 获取图标配置
     * @param {String} type 类型
     * @returns {Object} 图标配置
     */
    getIconConfig(type) {
        const configs = {
            success: {
                bgClass: 'bg-green-100',
                icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="size-6 text-green-600"><path d="m4.5 12.75 6 6 9-13.5" stroke-linecap="round" stroke-linejoin="round" /></svg>'
            },
            error: {
                bgClass: 'bg-red-100',
                icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="size-6 text-red-600"><path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" /></svg>'
            },
            warning: {
                bgClass: 'bg-yellow-100',
                icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="size-6 text-yellow-600"><path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke-linecap="round" stroke-linejoin="round" /></svg>'
            },
            info: {
                bgClass: 'bg-blue-100',
                icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="size-6 text-blue-600"><path d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0zm-9-3.75h.008v.008H12V8.25z" stroke-linecap="round" stroke-linejoin="round" /></svg>'
            }
        };

        return configs[type] || configs.info;
    }

    /**
     * 设置按钮
     * @param {Object} config 配置
     * @param {Function} resolve Promise resolve函数
     * @param {Boolean} isConfirm 是否为确认弹窗
     */
    setButtons(config, resolve, isConfirm) {
        const buttonsContainer = document.getElementById('dialog-buttons');
        
        if (isConfirm) {
            // 确认弹窗 - 两个按钮
            buttonsContainer.className = 'mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3';
            buttonsContainer.innerHTML = `
                <button type="button" id="dialog-confirm" class="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:col-start-2">
                    ${config.confirmText}
                </button>
                <button type="button" id="dialog-cancel" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0">
                    ${config.cancelText}
                </button>
            `;

            // 绑定事件
            document.getElementById('dialog-confirm').addEventListener('click', () => {
                this.close();
                resolve(true);
            });

            document.getElementById('dialog-cancel').addEventListener('click', () => {
                this.close();
                resolve(false);
            });
        } else {
            // 提示弹窗 - 一个按钮
            buttonsContainer.className = 'mt-5 sm:mt-6';
            buttonsContainer.innerHTML = `
                <button type="button" id="dialog-ok" class="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    ${config.confirmText}
                </button>
            `;

            // 绑定事件
            document.getElementById('dialog-ok').addEventListener('click', () => {
                this.close();
                resolve(true);
            });
        }

        // 背景点击关闭
        this.backdropElement.addEventListener('click', () => {
            this.close();
            resolve(false);
        });
    }

    /**
     * 打开弹窗
     */
    open() {
        // 显示弹窗
        this.dialogElement.classList.remove('hidden');

        // 使用requestAnimationFrame确保DOM更新完成后再添加动画
        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                // 添加动画类
                this.backdropElement.classList.remove('opacity-0');
                this.backdropElement.classList.add('opacity-100');

                this.panelElement.classList.remove('opacity-0', 'translate-y-4', 'sm:translate-y-0', 'sm:scale-95');
                this.panelElement.classList.add('opacity-100', 'translate-y-0', 'sm:scale-100');
            });
        });

        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭弹窗
     */
    close() {
        if (!this.currentDialog) return;

        // 移除动画类
        this.backdropElement.classList.remove('opacity-100');
        this.backdropElement.classList.add('opacity-0');
        
        this.panelElement.classList.remove('opacity-100', 'translate-y-0', 'sm:scale-100');
        this.panelElement.classList.add('opacity-0', 'translate-y-4', 'sm:translate-y-0', 'sm:scale-95');
        
        // 延迟隐藏
        setTimeout(() => {
            this.dialogElement.classList.add('hidden');
            // 恢复页面滚动
            document.body.style.overflow = '';
        }, 300);

        this.currentDialog = null;
    }
}

// 立即初始化函数
function initXunDialog() {
    if (!window.XunDialog) {
        window.XunDialog = new XunDialog();

        // 提供便捷方法
        window.xunConfirm = (options) => {
            return window.XunDialog.confirm(options);
        };
        window.xunAlert = (options) => {
            return window.XunDialog.alert(options);
        };
    }
}

// 立即尝试初始化
initXunDialog();

// 确保DOM加载完成后也初始化（双重保险）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initXunDialog);
} else {
    // DOM已经加载完成，再次确保初始化
    initXunDialog();
}
