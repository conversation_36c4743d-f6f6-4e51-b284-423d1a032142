/**
 * Xun Framework - 前端脚本
 *
 * 提供框架的前端交互功能，包括表单验证、AJAX提交等。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function() {
    'use strict';

    /**
     * 框架主对象
     */
    window.XunFramework = {

        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initHashNavigation();
            this.initMobileMenu();
            this.waitForDialog();
        },

        /**
         * 等待弹窗组件加载完成
         */
        waitForDialog: function() {
            // 如果弹窗组件没有加载，提供后备方案
            if (typeof window.xunConfirm === 'undefined') {
                window.xunConfirm = async function(options) {
                    return confirm(options.message || '确定要执行此操作吗？');
                };
            }

            if (typeof window.xunAlert === 'undefined') {
                window.xunAlert = async function(options) {
                    alert(options.message || '操作完成');
                    return true;
                };
            }
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            // AJAX表单提交
            const form = document.getElementById('xun-options-form');
            if (form) {
                form.addEventListener('submit', this.handleFormSubmit.bind(this));
            }

            // 重置按钮
            const resetBtn = document.querySelector('.xun-reset');
            if (resetBtn) {
                resetBtn.addEventListener('click', this.handleReset.bind(this));
            }

            // 导入配置按钮
            const importBtn = document.querySelector('.xun-import');
            if (importBtn) {
                importBtn.addEventListener('click', this.handleImport.bind(this));
            }

            // 导出配置按钮
            const exportBtn = document.querySelector('.xun-export');
            if (exportBtn) {
                exportBtn.addEventListener('click', this.handleExport.bind(this));
            }

            // 重置当前页面按钮
            const resetCurrentBtn = document.querySelector('.xun-reset-current');
            if (resetCurrentBtn) {
                resetCurrentBtn.addEventListener('click', this.handleResetCurrent.bind(this));
            }

            // 重置全部按钮
            const resetAllBtn = document.querySelector('.xun-reset-all');
            if (resetAllBtn) {
                resetAllBtn.addEventListener('click', this.handleResetAll.bind(this));
            }

            // 文件输入变化事件
            const fileInput = document.getElementById('xun-import-file');
            if (fileInput) {
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));
            }

            // 左侧菜单切换 - 支持子菜单的导航
            const navItems = document.querySelectorAll('.xun-nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', this.handleNavClick.bind(this));
            });

            // 父菜单展开/收起
            const parentItems = document.querySelectorAll('.xun-nav-parent');
            parentItems.forEach(item => {
                item.addEventListener('click', this.handleParentClick.bind(this));
            });

            // 键盘快捷键
            document.addEventListener('keydown', this.handleKeydown.bind(this));
        },

        /**
         * 处理表单提交 - AJAX无刷新保存
         */
        handleFormSubmit: function(e) {
            e.preventDefault();

            const form = e.target;
            // 由于按钮在表单外部，直接查找页面中的提交按钮
            const submitBtn = document.querySelector('.xun-submit');

            const formData = new FormData(form);

            // 添加AJAX标识
            formData.append('xun_ajax', '1');

            // 更新按钮状态为保存中
            this.updateButtonState(submitBtn, 'saving');

            // 发送AJAX请求
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateButtonState(submitBtn, 'success');
                } else {
                    this.updateButtonState(submitBtn, 'error');
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                this.updateButtonState(submitBtn, 'error');
            });
        },

        /**
         * 更新按钮状态
         */
        updateButtonState: function(button, state) {
            if (!button) return;

            // 存储原始内容（只在第一次调用时存储）
            if (!button.getAttribute('data-original-html')) {
                button.setAttribute('data-original-html', button.innerHTML);
                button.setAttribute('data-original-text', button.textContent.trim());
                // 存储原始宽度以防止抖动
                const computedStyle = window.getComputedStyle(button);
                button.setAttribute('data-original-width', computedStyle.width);
                button.style.minWidth = computedStyle.width;
            }

            const originalHtml = button.getAttribute('data-original-html');

            switch(state) {
                case 'saving':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span class="hidden xs:inline">保存中...</span>';
                    button.disabled = true;
                    button.classList.add('opacity-75', 'cursor-not-allowed');
                    break;
                case 'success':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="hidden xs:inline">保存成功</span>';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-green-600', 'hover:bg-green-700');
                    button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    setTimeout(() => {
                        button.innerHTML = originalHtml;
                        button.classList.remove('bg-green-600', 'hover:bg-green-700');
                        button.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    }, 2000);
                    break;
                case 'error':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span class="hidden xs:inline">保存失败</span>';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-red-600', 'hover:bg-red-700');
                    button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    setTimeout(() => {
                        button.innerHTML = originalHtml;
                        button.classList.remove('bg-red-600', 'hover:bg-red-700');
                        button.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    }, 2000);
                    break;
                default:
                    button.innerHTML = originalHtml;
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    break;
            }
        },

        /**
         * 处理重置
         */
        handleReset: function(e) {
            e.preventDefault();

            if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
                const form = document.getElementById('xun-options-form');
                const inputs = form.querySelectorAll('input, textarea, select');

                inputs.forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });

                alert('设置已重置');
            }
        },

        /**
         * 初始化哈希导航
         */
        initHashNavigation: function() {
            // 监听哈希变化
            window.addEventListener('hashchange', this.handleHashChange.bind(this));

            // 页面加载时检查哈希
            this.handleHashChange();
        },

        /**
         * 处理哈希变化
         */
        handleHashChange: function() {
            const hash = window.location.hash.substring(1); // 移除 # 符号
            const decodedHash = decodeURIComponent(hash); // 解码URL编码的中文

            if (!hash) {
                // 没有哈希，显示欢迎页面
                this.showWelcomePage();
                return;
            }

            // 特殊处理欢迎页哈希
            if (decodedHash === 'welcome') {
                this.showWelcomePage();
                return;
            }

            // 查找对应的菜单项
            const navItems = document.querySelectorAll('.xun-nav-item');
            let targetSectionIndex = null;

            navItems.forEach(item => {
                const sectionIndex = item.getAttribute('data-section');
                const slug = item.getAttribute('data-slug');

                if (slug === decodedHash) {
                    // 如果是数字，转换为整数；如果是字符串（如welcome），保持原样
                    targetSectionIndex = isNaN(parseInt(sectionIndex)) ? sectionIndex : parseInt(sectionIndex);
                }
            });

            if (targetSectionIndex !== null) {
                this.switchToSection(targetSectionIndex);
            } else {
                // 哈希不匹配任何菜单项，显示欢迎页面
                this.showWelcomePage();
            }
        },

        /**
         * 处理导入配置
         */
        handleImport: function(e) {
            e.preventDefault();
            const fileInput = document.getElementById('xun-import-file');
            if (fileInput) {
                fileInput.click();
            }
        },

        /**
         * 处理文件选择
         */
        handleFileSelect: async function(e) {
            const file = e.target.files[0];
            if (!file) return;

            if (file.type !== 'application/json') {
                await xunAlert({
                    title: '文件格式错误',
                    message: '请选择JSON格式的配置文件',
                    type: 'error'
                });
                return;
            }

            const reader = new FileReader();
            reader.onload = async (event) => {
                try {
                    const importData = event.target.result;
                    this.importConfiguration(importData);
                } catch (error) {
                    await xunAlert({
                        title: '文件读取错误',
                        message: '配置文件格式错误，请检查文件内容',
                        type: 'error'
                    });
                }
            };
            reader.readAsText(file);
        },

        /**
         * 导入配置
         */
        importConfiguration: async function(importData) {
            const confirmed = await xunConfirm({
                title: '确认导入配置',
                message: '导入配置将覆盖当前所有设置，此操作不可撤销。确定要继续吗？',
                confirmText: '确定导入',
                cancelText: '取消',
                type: 'warning'
            });

            if (!confirmed) {
                return;
            }

            const importBtn = document.querySelector('.xun-import');
            this.updateImportButtonState(importBtn, 'importing');

            const formData = new FormData();
            formData.append('action', 'xun_import_options');
            formData.append('import_data', importData);

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    this.updateImportButtonState(importBtn, 'success');
                    await xunAlert({
                        title: '导入成功',
                        message: data.message,
                        type: 'success'
                    });
                    location.reload(); // 刷新页面以显示新配置
                } else {
                    this.updateImportButtonState(importBtn, 'error');
                    await xunAlert({
                        title: '导入失败',
                        message: data.message || '导入过程中发生错误',
                        type: 'error'
                    });
                    setTimeout(() => {
                        this.updateImportButtonState(importBtn, 'default');
                    }, 2000);
                }
            })
            .catch(async error => {
                this.updateImportButtonState(importBtn, 'error');
                await xunAlert({
                    title: '导入失败',
                    message: '导入过程中发生网络错误，请稍后重试',
                    type: 'error'
                });
                setTimeout(() => {
                    this.updateImportButtonState(importBtn, 'default');
                }, 2000);
            });
        },

        /**
         * 处理导出配置
         */
        handleExport: function(e) {
            e.preventDefault();

            const exportBtn = e.currentTarget;
            this.updateExportButtonState(exportBtn, 'exporting');

            const formData = new FormData();
            formData.append('action', 'xun_export_options');

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    this.updateExportButtonState(exportBtn, 'success');
                    this.downloadJSON(data.data, data.filename);
                    setTimeout(() => {
                        this.updateExportButtonState(exportBtn, 'default');
                    }, 2000);
                } else {
                    this.updateExportButtonState(exportBtn, 'error');
                    await xunAlert({
                        title: '导出失败',
                        message: data.message || '导出过程中发生错误',
                        type: 'error'
                    });
                    setTimeout(() => {
                        this.updateExportButtonState(exportBtn, 'default');
                    }, 2000);
                }
            })
            .catch(async error => {
                this.updateExportButtonState(exportBtn, 'error');
                await xunAlert({
                    title: '导出失败',
                    message: '导出过程中发生网络错误，请稍后重试',
                    type: 'error'
                });
                setTimeout(() => {
                    this.updateExportButtonState(exportBtn, 'default');
                }, 2000);
            });
        },

        /**
         * 下载JSON文件
         */
        downloadJSON: function(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        },

        /**
         * 处理重置当前页面
         */
        handleResetCurrent: async function(e) {
            e.preventDefault();

            const confirmed = await xunConfirm({
                title: '重置当前页面',
                message: '确定要重置当前页面的所有设置吗？此操作将恢复为默认值。',
                confirmText: '确定重置',
                cancelText: '取消',
                type: 'warning'
            });

            if (!confirmed) {
                return;
            }

            // 获取当前激活的section索引
            const activeItem = document.querySelector('.xun-nav-item .xun-nav-indicator.opacity-100');
            let sectionIndex = 0;
            if (activeItem) {
                const navItem = activeItem.closest('.xun-nav-item');
                sectionIndex = parseInt(navItem.getAttribute('data-section')) || 0;
            }

            const formData = new FormData();
            formData.append('action', 'xun_reset_current');
            formData.append('section_index', sectionIndex);
            formData.append('_wpnonce', window.xunAjax?.nonce || '');

            fetch(window.xunAjax?.ajaxurl || '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    await xunAlert({
                        title: '重置成功',
                        message: data.message,
                        type: 'success'
                    });
                    // 刷新页面并强制清除缓存
                    const url = new URL(window.location);
                    url.searchParams.set('xun_refresh', '1');
                    window.location.href = url.toString();
                } else {
                    await xunAlert({
                        title: '重置失败',
                        message: data.message || '重置过程中发生错误',
                        type: 'error'
                    });
                }
            })
            .catch(async error => {
                await xunAlert({
                    title: '重置失败',
                    message: '重置过程中发生网络错误，请稍后重试',
                    type: 'error'
                });
            });
        },

        /**
         * 处理重置全部配置
         */
        handleResetAll: async function(e) {
            e.preventDefault();

            const confirmed = await xunConfirm({
                title: '危险操作',
                message: '警告：这将删除所有配置数据！此操作不可撤销，确定要重置全部设置吗？',
                confirmText: '确定删除全部',
                cancelText: '取消',
                type: 'error'
            });

            if (!confirmed) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'xun_reset_all');
            formData.append('_wpnonce', window.xunAjax?.nonce || '');

            fetch(window.xunAjax?.ajaxurl || '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    await xunAlert({
                        title: '重置成功',
                        message: data.message,
                        type: 'success'
                    });
                    // 刷新页面并强制清除缓存
                    const url = new URL(window.location);
                    url.searchParams.set('xun_refresh', '1');
                    window.location.href = url.toString();
                } else {
                    await xunAlert({
                        title: '重置失败',
                        message: data.message || '重置过程中发生错误',
                        type: 'error'
                    });
                }
            })
            .catch(async error => {
                await xunAlert({
                    title: '重置失败',
                    message: '重置过程中发生网络错误，请稍后重试',
                    type: 'error'
                });
            });
        },

        /**
         * 更新导入按钮状态
         */
        updateImportButtonState: function(button, state) {
            if (!button) return;

            // 存储原始内容（只在第一次调用时存储）
            if (!button.getAttribute('data-original-html')) {
                button.setAttribute('data-original-html', button.innerHTML);
            }

            const originalHtml = button.getAttribute('data-original-html');

            switch(state) {
                case 'importing':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>导入中...';
                    button.disabled = true;
                    button.classList.add('opacity-75', 'cursor-not-allowed', 'w-26', 'whitespace-nowrap', 'justify-center');
                    break;
                case 'success':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>导入成功';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-green-600', 'hover:bg-green-700', 'text-white', 'border-green-600');
                    button.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
                case 'error':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>导入失败';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-red-600', 'hover:bg-red-700', 'text-white', 'border-red-600');
                    button.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
                default:
                    button.innerHTML = originalHtml;
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed', 'w-26', 'whitespace-nowrap', 'justify-center');
                    button.classList.remove('bg-green-600', 'hover:bg-green-700', 'bg-red-600', 'hover:bg-red-700', 'text-white', 'border-green-600', 'border-red-600');
                    button.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
            }
        },

        /**
         * 更新导出按钮状态
         */
        updateExportButtonState: function(button, state) {
            if (!button) return;

            // 存储原始内容（只在第一次调用时存储）
            if (!button.getAttribute('data-original-html')) {
                button.setAttribute('data-original-html', button.innerHTML);
            }

            const originalHtml = button.getAttribute('data-original-html');

            switch(state) {
                case 'exporting':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>导出中...';
                    button.disabled = true;
                    button.classList.add('opacity-75', 'cursor-not-allowed', 'w-26', 'whitespace-nowrap', 'justify-center');
                    break;
                case 'success':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>导出成功';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-green-600', 'hover:bg-green-700', 'text-white', 'border-green-600');
                    button.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
                case 'error':
                    button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>导出失败';
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');
                    button.classList.add('bg-red-600', 'hover:bg-red-700', 'text-white', 'border-red-600');
                    button.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
                default:
                    button.innerHTML = originalHtml;
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed', 'w-26', 'whitespace-nowrap', 'justify-center');
                    button.classList.remove('bg-green-600', 'hover:bg-green-700', 'bg-red-600', 'hover:bg-red-700', 'text-white', 'border-green-600', 'border-red-600');
                    button.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-700', 'border-gray-300');
                    break;
            }
        },

        /**
         * 生成URL slug
         */
        generateSlug: function(text) {
            return text.toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w\-\u4e00-\u9fa5]/g, '')
                .replace(/\-+/g, '-')
                .replace(/^-|-$/g, '');
        },

        /**
         * 显示欢迎页面
         */
        showWelcomePage: function() {
            // 隐藏所有section
            document.querySelectorAll('.xun-section-wrapper').forEach(wrapper => {
                wrapper.classList.remove('block');
                wrapper.classList.add('hidden');
            });

            // 显示欢迎页面
            const welcomePage = document.querySelector('.xun-section-wrapper[data-section="welcome"]');
            if (welcomePage) {
                welcomePage.classList.remove('hidden');
                welcomePage.classList.add('block');
            }

            // 更新菜单项激活状态
            document.querySelectorAll('.xun-nav-item').forEach(item => {
                const itemSection = item.getAttribute('data-section');
                const indicator = item.querySelector('.xun-nav-indicator');
                const content = item.querySelector('.xun-nav-content');
                const desc = item.querySelector('.xun-nav-desc');
                const isChild = item.classList.contains('xun-nav-child');

                if (itemSection === 'welcome') {
                    // 激活欢迎页菜单项
                    if (indicator) {
                        indicator.classList.remove('opacity-0');
                        indicator.classList.add('opacity-100');
                    }
                    if (content) {
                        content.classList.remove('text-gray-700', 'hover:bg-gray-50', 'border-transparent');
                        content.classList.add('bg-blue-50', 'text-blue-700', 'border-blue-200');
                    }
                    if (desc) {
                        desc.classList.remove('text-gray-500');
                        desc.classList.add('text-blue-600');
                    }
                } else {
                    // 取消其他菜单项的激活状态
                    if (indicator) {
                        indicator.classList.remove('opacity-100');
                        indicator.classList.add('opacity-0');
                    }
                    if (content) {
                        if (isChild) {
                            content.classList.remove('bg-blue-50', 'text-blue-700', 'border-blue-200');
                            content.classList.add('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-700', 'border-transparent');
                        } else {
                            content.classList.remove('bg-blue-50', 'text-blue-700', 'border-blue-200');
                            content.classList.add('text-gray-700', 'hover:bg-gray-50', 'border-transparent');
                        }
                    }
                    if (desc) {
                        desc.classList.remove('text-blue-600');
                        desc.classList.add('text-gray-500');
                    }
                }
            });

            // 更新面包屑
            const breadcrumbCurrent = document.querySelector('.xun-breadcrumb-current');
            if (breadcrumbCurrent) {
                breadcrumbCurrent.textContent = '欢迎';
            }

            // 更新URL哈希为welcome
            if (window.location.hash !== '#welcome') {
                history.replaceState(null, null, window.location.pathname + window.location.search + '#welcome');
            }
        },

        /**
         * 处理导航点击
         */
        handleNavClick: function(e) {
            e.preventDefault();
            e.stopPropagation(); // 防止事件冒泡到父菜单

            const clickedItem = e.currentTarget;
            const sectionIndex = parseInt(clickedItem.getAttribute('data-section'));

            // 移除focus状态，避免描边
            clickedItem.blur();

            // 更新URL哈希
            const slug = clickedItem.getAttribute('data-slug');
            if (slug) {
                window.location.hash = slug;
            } else {
                // 如果没有slug，直接切换到section
                this.switchToSection(sectionIndex);
            }

        },

        /**
         * 处理父菜单点击（展开/收起子菜单）
         */
        handleParentClick: function(e) {
            e.preventDefault();
            e.stopPropagation();

            const parentItem = e.currentTarget;
            const group = parentItem.closest('.xun-nav-group');
            const childrenContainer = group.querySelector('.xun-nav-children');
            const arrow = parentItem.querySelector('svg');

            if (childrenContainer) {
                const isHidden = childrenContainer.classList.contains('hidden');

                if (isHidden) {
                    // 展开子菜单 - 丝滑动画
                    this.expandMenu(childrenContainer, arrow);
                } else {
                    // 收起子菜单 - 丝滑动画
                    this.collapseMenu(childrenContainer, arrow);
                }
            }
        },

        /**
         * 展开菜单动画
         */
        expandMenu: function(container, arrow) {
            // 先设置为可见但高度为0
            container.style.maxHeight = '0px';
            container.classList.remove('hidden');
            container.classList.add('block');

            // 获取实际内容高度
            const scrollHeight = container.scrollHeight;

            // 使用requestAnimationFrame确保平滑动画
            requestAnimationFrame(() => {
                container.style.maxHeight = scrollHeight + 'px';
                if (arrow) {
                    arrow.style.transform = 'rotate(90deg)';
                }
            });

            // 动画完成后移除内联样式，让CSS接管
            setTimeout(() => {
                container.style.maxHeight = '';
            }, 300);
        },

        /**
         * 收起菜单动画
         */
        collapseMenu: function(container, arrow) {
            // 获取当前高度并立即设置
            const currentHeight = container.scrollHeight;
            container.style.maxHeight = currentHeight + 'px';
            container.style.transition = 'none'; // 暂时禁用过渡

            // 立即应用高度，然后重新启用过渡
            requestAnimationFrame(() => {
                container.style.transition = ''; // 恢复CSS过渡
                container.style.maxHeight = '0px';
                if (arrow) {
                    arrow.style.transform = 'rotate(0deg)';
                }
            });

            // 动画完成后隐藏元素
            setTimeout(() => {
                container.classList.remove('block');
                container.classList.add('hidden');
                container.style.maxHeight = '';
                container.style.transition = '';
            }, 300);
        },

        /**
         * 切换到指定区块
         */
        switchToSection: function(sectionIndex) {
            // 如果sectionIndex为'welcome'，显示欢迎页
            if (sectionIndex === 'welcome') {
                this.showWelcomePage();
                return;
            }

            // 如果sectionIndex为-1，说明这是父菜单项，不渲染内容
            if (sectionIndex === -1) {
                return;
            }

            const contentArea = document.querySelector('.flex-1.p-6.bg-gray-50');

            // 检查是否真的需要切换（避免重复切换同一个section）
            const currentActiveItem = document.querySelector('.xun-nav-item .xun-nav-indicator.opacity-100');
            if (currentActiveItem) {
                const currentSectionIndex = parseInt(currentActiveItem.closest('.xun-nav-item').getAttribute('data-section'));
                if (currentSectionIndex === sectionIndex) {
                    return; // 已经是当前激活的section，无需切换
                }
            }

            // 隐藏欢迎页面
            const welcomePage = document.querySelector('.xun-section-wrapper[data-section="welcome"]');
            if (welcomePage) {
                welcomePage.classList.remove('block');
                welcomePage.classList.add('hidden');
            }

            // 添加切换动画类
            if (contentArea) {
                contentArea.style.opacity = '0.9';
                contentArea.style.transition = 'opacity 0.15s ease';
            }

            // 延迟执行切换，创建平滑效果
            setTimeout(() => {
                // 更新导航状态 - 支持子菜单的设计
                document.querySelectorAll('.xun-nav-item').forEach((item) => {
                    const itemSectionIndex = parseInt(item.getAttribute('data-section'));
                    const indicator = item.querySelector('.xun-nav-indicator');
                    const content = item.querySelector('.xun-nav-content');
                    const desc = item.querySelector('.xun-nav-desc');
                    const isChild = item.classList.contains('xun-nav-child');

                    if (itemSectionIndex === sectionIndex) {
                        // 激活状态
                        if (indicator) {
                            indicator.classList.remove('opacity-0');
                            indicator.classList.add('opacity-100');
                        }
                        if (content) {
                            if (isChild) {
                                // 子菜单项的激活样式
                                content.classList.remove('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-700', 'border-transparent');
                                content.classList.add('bg-blue-50', 'text-blue-700', 'border-blue-200');
                            } else {
                                // 普通菜单项的激活样式
                                content.classList.remove('text-gray-700', 'hover:bg-gray-50', 'border-transparent');
                                content.classList.add('bg-blue-50', 'text-blue-700', 'border-blue-200');
                            }
                        }
                        if (desc) {
                            desc.classList.remove('text-gray-500');
                            desc.classList.add('text-blue-600');
                        }

                        // 如果是子菜单项，确保其父菜单是展开的
                        if (isChild) {
                            const parentGroup = item.closest('.xun-nav-group');
                            if (parentGroup) {
                                const childrenContainer = parentGroup.querySelector('.xun-nav-children');
                                const arrow = parentGroup.querySelector('.xun-nav-parent svg');
                                if (childrenContainer && childrenContainer.classList.contains('hidden')) {
                                    // 使用丝滑动画展开父菜单
                                    this.expandMenu(childrenContainer, arrow);
                                }
                            }
                        }
                    } else {
                        // 非激活状态
                        if (indicator) {
                            indicator.classList.remove('opacity-100');
                            indicator.classList.add('opacity-0');
                        }
                        if (content) {
                            if (isChild) {
                                // 子菜单项的非激活样式
                                content.classList.remove('bg-blue-50', 'text-blue-700', 'border-blue-200');
                                content.classList.add('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-700', 'border-transparent');
                            } else {
                                // 普通菜单项的非激活样式
                                content.classList.remove('bg-blue-50', 'text-blue-700', 'border-blue-200');
                                content.classList.add('text-gray-700', 'hover:bg-gray-50', 'border-transparent');
                            }
                        }
                        if (desc) {
                            desc.classList.remove('text-blue-600');
                            desc.classList.add('text-gray-500');
                        }
                    }
                });

                // 切换内容区块 - 使用data-section属性匹配
                document.querySelectorAll('.xun-section-wrapper').forEach((wrapper) => {
                    const wrapperSectionIndex = wrapper.getAttribute('data-section');
                    if (wrapperSectionIndex == sectionIndex) { // 使用==而不是===，因为可能是字符串vs数字
                        wrapper.classList.remove('hidden');
                        wrapper.classList.add('block');
                    } else {
                        wrapper.classList.remove('block');
                        wrapper.classList.add('hidden');
                    }
                });

                // 更新面包屑导航
                const navItems = document.querySelectorAll('.xun-nav-item');
                const breadcrumbCurrent = document.querySelector('.xun-breadcrumb-current');

                if (breadcrumbCurrent) {
                    // 查找对应的菜单项
                    let targetItem = null;
                    navItems.forEach(item => {
                        const itemSectionIndex = parseInt(item.getAttribute('data-section'));
                        if (itemSectionIndex === sectionIndex) {
                            targetItem = item;
                        }
                    });

                    if (targetItem) {
                        const titleElement = targetItem.querySelector('.font-medium');
                        const slug = targetItem.getAttribute('data-slug');

                        if (titleElement) {
                            breadcrumbCurrent.textContent = titleElement.textContent;
                        }

                        // 更新URL哈希（如果不是通过哈希变化触发的）
                        if (slug && window.location.hash.substring(1) !== slug) {
                            window.location.hash = slug;
                        }
                    }
                }



                // 滚动到顶部
                if (contentArea) {
                    contentArea.scrollTop = 0;
                    // 恢复透明度
                    contentArea.style.opacity = '1';
                }
            }, 50); // 减少延迟时间，让切换更流畅
        },

        /**
         * 初始化选项卡
         */
        initTabs: function() {
            // 检查URL中是否有哈希，如果有则直接导航到对应页面，否则显示欢迎页
            const hash = window.location.hash.substring(1);
            if (hash) {
                // 有哈希，直接处理哈希导航，不显示欢迎页
                const decodedHash = decodeURIComponent(hash);

                // 特殊处理欢迎页哈希
                if (decodedHash === 'welcome') {
                    this.showWelcomePage();
                    return;
                }

                // 查找对应的菜单项
                const navItems = document.querySelectorAll('.xun-nav-item');
                let targetSectionIndex = null;

                navItems.forEach(item => {
                    const sectionIndex = item.getAttribute('data-section');
                    const slug = item.getAttribute('data-slug');

                    if (slug === decodedHash) {
                        // 如果是数字，转换为整数；如果是字符串（如welcome），保持原样
                        targetSectionIndex = isNaN(parseInt(sectionIndex)) ? sectionIndex : parseInt(sectionIndex);
                    }
                });

                if (targetSectionIndex !== null) {
                    this.switchToSection(targetSectionIndex);
                } else {
                    // 哈希不匹配任何菜单项，显示欢迎页面
                    this.showWelcomePage();
                }
            } else {
                // 没有哈希，默认显示欢迎页
                this.switchToSection('welcome');
            }
        },

        /**
         * 初始化移动端菜单
         */
        initMobileMenu: function() {
            const openBtn = document.getElementById('open-sidebar');
            const closeBtn = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            if (!openBtn || !closeBtn || !sidebar || !overlay) {
                return;
            }

            // 打开侧边栏
            const openSidebar = () => {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            };

            // 关闭侧边栏
            const closeSidebar = () => {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.style.overflow = '';
            };

            // 绑定事件
            openBtn.addEventListener('click', openSidebar);
            closeBtn.addEventListener('click', closeSidebar);
            overlay.addEventListener('click', closeSidebar);

            // ESC键关闭
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
                    closeSidebar();
                }
            });

            // 窗口大小改变时处理
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) { // lg断点
                    closeSidebar();
                }
            });

            // 点击菜单项时在移动端关闭侧边栏
            const navItems = document.querySelectorAll('.xun-nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
        },

        /**
         * 处理键盘快捷键
         */
        handleKeydown: function(e) {
            // 只在没有焦点在输入框时响应快捷键
            if (document.activeElement.tagName === 'INPUT' ||
                document.activeElement.tagName === 'TEXTAREA' ||
                document.activeElement.tagName === 'SELECT') {
                return;
            }

            const totalSections = document.querySelectorAll('.xun-section-wrapper').length;
            const currentActive = document.querySelector('.xun-nav-item .xun-nav-content.bg-blue-50');
            const currentIndex = currentActive ? parseInt(currentActive.closest('.xun-nav-item').getAttribute('data-section')) : 0;

            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : totalSections - 1;
                    this.switchToSection(prevIndex);
                    break;

                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < totalSections - 1 ? currentIndex + 1 : 0;
                    this.switchToSection(nextIndex);
                    break;

                case 'Home':
                    e.preventDefault();
                    this.switchToSection(0);
                    break;

                case 'End':
                    e.preventDefault();
                    this.switchToSection(totalSections - 1);
                    break;
            }
        }
    };

    // 文档就绪时初始化
    document.addEventListener('DOMContentLoaded', function() {
        XunFramework.init();
    });

})();