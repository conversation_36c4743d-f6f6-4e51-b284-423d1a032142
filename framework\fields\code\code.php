<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Xun Framework 代码字段类型
 *
 * 这个类实现了简洁的代码编辑字段功能，使用TailwindCSS样式。
 * 提供基础的代码编辑体验，无需第三方依赖。
 *
 * 特性：
 * - 使用原生textarea，轻量级实现
 * - TailwindCSS现代化样式
 * - 等宽字体显示
 * - 语法高亮提示（通过CSS）
 * - 行号显示（可选）
 * - 自动缩进保持
 * - 代码格式化按钮
 * - 全屏编辑模式
 * - 响应式设计
 * - 无障碍访问支持
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_code' ) ) {

    /**
     * XUN_Field_code 代码字段类
     *
     * 提供简洁的代码编辑功能，包括：
     * - 等宽字体显示
     * - 语法提示
     * - 行号显示
     * - 代码格式化
     * - 全屏编辑
     * - 主题切换
     *
     * @since 1.0
     */
    class XUN_Field_code extends XUN_Fields {

        /**
         * 构造函数
         *
         * 初始化代码字段实例。
         *
         * @since 1.0
         *
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }

        /**
         * 渲染代码字段
         *
         * 输出现代化的代码编辑字段HTML。
         *
         * @since 1.0
         */
        public function render() {

            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'language'        => 'html',              // 编程语言类型
                'theme'           => 'light',             // 主题: light, dark
                'height'          => 300,                 // 编辑器高度（像素）
                'show_line_numbers' => true,              // 是否显示行号
                'tab_size'        => 2,                   // Tab缩进大小
                'placeholder'     => '请输入代码...',      // 占位符文字
                'readonly'        => false,               // 是否只读
                'fullscreen'      => true,                // 是否支持全屏
                'format_button'   => true,                // 是否显示格式化按钮
                'copy_button'     => true,                // 是否显示复制按钮
                'word_wrap'       => true,                // 是否自动换行
                'font_size'       => 14,                  // 字体大小
            ) );

            // 生成唯一ID
            $field_id = $this->field_id();
            $textarea_id = $field_id . '_textarea';

            // 处理主题样式
            $theme_class = $args['theme'] === 'dark' ? 'bg-gray-900 text-green-400' : 'bg-gray-50 text-gray-900';
            $border_class = $args['theme'] === 'dark' ? 'border-gray-700' : 'border-gray-300';

            // 输出字段前置内容
            echo $this->field_before();

            // 开始代码字段容器
            echo '<div class="xun-code-field" data-field-id="' . esc_attr( $field_id ) . '" data-language="' . esc_attr( $args['language'] ) . '" data-theme="' . esc_attr( $args['theme'] ) . '">';

            // 响应式flex布局 - 确保全屏模式下底部操作栏在底部
            echo '<div class="xun-code-inner h-full min-h-0 flex flex-col">';

            // 工具栏头部
            if ( $args['format_button'] || $args['copy_button'] || $args['fullscreen'] ) {
                echo '<div class="xun-code-header-wrapper flex-shrink-0">';
                $this->render_toolbar( $args, $field_id );
                echo '</div>';
            }

            // 代码编辑内容区域 - 使用flex-1让它占据剩余空间
            echo '<div class="xun-code-content flex-1 overflow-hidden">';
            $this->render_editor( $args, $textarea_id );
            echo '</div>';

            echo '</div>'; // 关闭inner容器
            echo '</div>'; // 关闭主容器

            // 输出字段后置内容
            echo $this->field_after();
        }

        /**
         * 渲染工具栏
         *
         * @since 1.0
         *
         * @param array  $args     字段配置参数
         * @param string $field_id 字段ID
         */
        private function render_toolbar( $args, $field_id ) {

            echo '<div class="xun-code-toolbar flex items-center justify-between p-3 bg-gray-100 border border-b-0 border-gray-300 rounded-t-md">';

            // 左侧：语言标签
            echo '<div class="flex items-center space-x-2">';
            echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">';
            echo esc_html( strtoupper( $args['language'] ) );
            echo '</span>';

            if ( $args['show_line_numbers'] ) {
                echo '<span class="text-xs text-gray-500">行号</span>';
            }
            echo '</div>';

            // 右侧：操作按钮
            echo '<div class="flex items-center space-x-2">';

            // 复制按钮
            if ( $args['copy_button'] ) {
                echo '<button type="button" class="xun-code-copy inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" title="复制代码">';
                echo '<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>';
                echo '</svg>';
                echo '复制';
                echo '</button>';
            }

            // 格式化按钮
            if ( $args['format_button'] ) {
                echo '<button type="button" class="xun-code-format inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" title="格式化代码">';
                echo '<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>';
                echo '</svg>';
                echo '格式化';
                echo '</button>';
            }

            // 全屏按钮
            if ( $args['fullscreen'] ) {
                echo '<button type="button" class="xun-code-fullscreen inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" title="全屏编辑">';
                echo '<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>';
                echo '</svg>';
                echo '全屏';
                echo '</button>';
            }

            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染编辑器区域
         *
         * @since 1.0
         *
         * @param array  $args        字段配置参数
         * @param string $textarea_id textarea元素ID
         */
        private function render_editor( $args, $textarea_id ) {

            // 处理主题样式
            $theme_class = $args['theme'] === 'dark'
                ? 'bg-gray-900 text-green-400 border-gray-700'
                : 'bg-gray-50 text-gray-900 border-gray-300';

            // 计算高度
            $height_style = 'height: ' . intval( $args['height'] ) . 'px;';

            // 字体大小
            $font_size_style = 'font-size: ' . intval( $args['font_size'] ) . 'px;';

            // Tab大小
            $tab_size_style = 'tab-size: ' . intval( $args['tab_size'] ) . ';';

            // 编辑器容器 - 使用flex布局避免行号覆盖
            echo '<div class="xun-code-editor-container flex h-full" style="' . esc_attr( $height_style ) . '" data-normal-height="' . esc_attr( $height_style ) . '">';

            // 行号区域（如果启用）
            if ( $args['show_line_numbers'] ) {
                echo '<div class="xun-code-line-numbers flex-shrink-0 w-12 ' . esc_attr( $theme_class ) . ' border-r text-xs leading-5 text-center py-3 select-none overflow-hidden" style="' . esc_attr( $font_size_style ) . '">';
                echo '<div class="xun-line-numbers-content"></div>';
                echo '</div>';
            }

            // 主编辑区域 - 使用flex-1占据剩余空间
            echo '<div class="flex-1 relative">';
            echo '<textarea ';
            echo 'id="' . esc_attr( $textarea_id ) . '" ';
            echo 'name="' . esc_attr( $this->field_name() ) . '" ';
            echo 'class="xun-code-textarea block w-full h-full px-4 py-3 font-mono text-sm leading-5 resize-none border-0 focus:outline-none focus:ring-0 ' . esc_attr( $theme_class ) . '" ';
            echo 'style="' . esc_attr( $font_size_style . $tab_size_style ) . '" ';
            echo 'placeholder="' . esc_attr( $args['placeholder'] ) . '" ';
            echo 'spellcheck="false" ';
            echo 'autocomplete="off" ';
            echo 'autocorrect="off" ';
            echo 'autocapitalize="off" ';
            echo 'data-language="' . esc_attr( $args['language'] ) . '" ';
            echo 'data-tab-size="' . esc_attr( $args['tab_size'] ) . '" ';
            echo 'data-word-wrap="' . ( $args['word_wrap'] ? 'true' : 'false' ) . '" ';
            if ( $args['readonly'] ) {
                echo 'readonly ';
            }
            echo $this->field_attributes();
            echo '>';
            echo esc_textarea( $this->value );
            echo '</textarea>';
            echo '</div>';

            echo '</div>';
        }

        /**
         * 加载字段资源
         *
         * 加载代码字段所需的CSS和JavaScript文件。
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载字段专用JavaScript
            wp_enqueue_script(
                'xun-field-code',
                XUN_Setup::$url . '/assets/js/fields/code.js',
                array( 'jquery' ),
                XUN_VERSION,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-field-code', 'xunCodeField', array(
                'copy_success'    => __( '代码已复制到剪贴板', 'xun' ),
                'copy_error'      => __( '复制失败，请手动复制', 'xun' ),
                'format_success'  => __( '代码格式化完成', 'xun' ),
                'format_error'    => __( '格式化失败', 'xun' ),
                'fullscreen_enter' => __( '进入全屏', 'xun' ),
                'fullscreen_exit'  => __( '退出全屏', 'xun' ),
            ) );
        }

        /**
         * 验证字段值
         *
         * 验证和清理代码字段值。
         *
         * @since 1.0
         *
         * @param mixed $value 要验证的值
         *
         * @return mixed 验证后的值
         */
        public function validate( $value ) {

            // 基本的字符串清理，保留代码格式
            if ( is_string( $value ) ) {

                // 首先处理可能的magic quotes问题
                // WordPress在某些情况下会自动添加反斜杠，我们需要移除它们
                if ( function_exists( 'get_magic_quotes_gpc' ) && get_magic_quotes_gpc() ) {
                    $value = stripslashes( $value );
                }

                // 如果数据来自POST请求，可能需要额外的stripslashes处理
                // 检查是否有过度转义的情况
                if ( strpos( $value, '\\\\' ) !== false ) {
                    // 移除多余的反斜杠，但保留必要的转义
                    $value = stripslashes( $value );
                }

                // 限制内容大小（32KB）
                if ( strlen( $value ) > 32768 ) {
                    $value = substr( $value, 0, 32768 );
                }

                // 对于代码字段，我们不使用wp_kses，因为它会破坏代码格式
                // 相反，我们只进行基本的安全检查

                // 移除可能的恶意PHP标签（如果不是PHP代码字段）
                $language = isset( $this->field['language'] ) ? $this->field['language'] : 'html';
                if ( $language !== 'php' ) {
                    // 移除PHP开始和结束标签
                    $value = preg_replace( '/<\?(?:php)?/i', '&lt;?', $value );
                    $value = preg_replace( '/\?>/i', '?&gt;', $value );
                }

                // 移除可能的恶意脚本标签（如果不是JavaScript代码字段）
                if ( $language !== 'javascript' && $language !== 'js' ) {
                    $value = preg_replace( '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value );
                }

                return $value;
            }

            return '';
        }

        /**
         * 获取字段配置示例
         *
         * 返回代码字段的配置示例，用于文档和开发参考。
         *
         * @since 1.0
         *
         * @return array 配置示例数组
         */
        public static function get_config_example() {

            return array(
                'id'               => 'code_field_example',
                'type'             => 'code',
                'title'            => '代码字段示例',
                'desc'             => '用于输入和编辑代码的字段',
                'language'         => 'html',              // 编程语言类型
                'theme'            => 'light',             // 主题: light, dark
                'height'           => 300,                 // 编辑器高度（像素）
                'show_line_numbers' => true,               // 是否显示行号
                'tab_size'         => 2,                   // Tab缩进大小
                'placeholder'      => '请输入HTML代码...', // 占位符文字
                'readonly'         => false,               // 是否只读
                'fullscreen'       => true,                // 是否支持全屏
                'format_button'    => true,                // 是否显示格式化按钮
                'copy_button'      => true,                // 是否显示复制按钮
                'word_wrap'        => true,                // 是否自动换行
                'font_size'        => 14,                  // 字体大小
                'default'          => '<!DOCTYPE html>\n<html>\n<head>\n    <title>示例</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>', // 默认值
            );
        }
    }
}